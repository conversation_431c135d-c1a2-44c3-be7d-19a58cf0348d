'use client'

import { useState } from 'react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { SubmitButton } from '@/components/auth/submit-button'
import {
  validateBusinessName,
  validateBusinessDescription,
  validatePostalCode,
  validatePhone,
  validateWebsite,
  validateEmail,
} from '@/lib/validation'

type BusinessFormData = {
  name: string
  description: string
  category: string
  address: {
    street: string
    city: string
    province: string
    postalCode: string
  }
  contact: {
    phone: string
    email: string
    website: string
  }
  hours: {
    monday: string
    tuesday: string
    wednesday: string
    thursday: string
    friday: string
    saturday: string
    sunday: string
  }
}

const BUSINESS_CATEGORIES = [
  { label: 'Restaurant & Food', value: 'restaurant' },
  { label: 'Retail & Shopping', value: 'retail' },
  { label: 'Health & Wellness', value: 'health' },
  { label: 'Professional Services', value: 'professional' },
  { label: 'Home & Garden', value: 'home' },
  { label: 'Automotive', value: 'automotive' },
  { label: 'Beauty & Personal Care', value: 'beauty' },
  { label: 'Entertainment & Recreation', value: 'entertainment' },
  { label: 'Education & Training', value: 'education' },
  { label: 'Technology', value: 'technology' },
  { label: 'Real Estate', value: 'realestate' },
  { label: 'Financial Services', value: 'financial' },
  { label: 'Other', value: 'other' },
]

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' },
]

export const BusinessForm = () => {
  const [isPending, setIsPending] = useState(false)
  const [formData, setFormData] = useState<BusinessFormData>({
    name: '',
    description: '',
    category: '',
    address: {
      street: '',
      city: 'Toronto',
      province: 'Ontario',
      postalCode: '',
    },
    contact: {
      phone: '',
      email: '',
      website: '',
    },
    hours: {
      monday: '',
      tuesday: '',
      wednesday: '',
      thursday: '',
      friday: '',
      saturday: '',
      sunday: '',
    },
  })
  const router = useRouter()

  const updateFormData = (path: string, value: string) => {
    setFormData((prev) => {
      const keys = path.split('.')
      const newData = { ...prev }
      let current: any = newData

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]]
      }
      current[keys[keys.length - 1]] = value

      return newData
    })
  }

  const validateForm = (): boolean => {
    // Validate required fields
    const nameValidation = validateBusinessName(formData.name)
    if (!nameValidation.valid) {
      toast.error('Invalid Business Name', { description: nameValidation.error })
      return false
    }

    const descriptionValidation = validateBusinessDescription(formData.description)
    if (!descriptionValidation.valid) {
      toast.error('Invalid Description', { description: descriptionValidation.error })
      return false
    }

    if (!formData.category) {
      toast.error('Category Required', { description: 'Please select a business category' })
      return false
    }

    if (!formData.address.street.trim()) {
      toast.error('Address Required', { description: 'Street address is required' })
      return false
    }

    const postalCodeValidation = validatePostalCode(formData.address.postalCode)
    if (!postalCodeValidation.valid) {
      toast.error('Invalid Postal Code', { description: postalCodeValidation.error })
      return false
    }

    // Validate optional fields if provided
    if (formData.contact.phone) {
      const phoneValidation = validatePhone(formData.contact.phone)
      if (!phoneValidation.valid) {
        toast.error('Invalid Phone Number', { description: phoneValidation.error })
        return false
      }
    }

    if (formData.contact.email) {
      const emailValidation = validateEmail(formData.contact.email)
      if (!emailValidation.valid) {
        toast.error('Invalid Email', { description: emailValidation.error })
        return false
      }
    }

    if (formData.contact.website) {
      const websiteValidation = validateWebsite(formData.contact.website)
      if (!websiteValidation.valid) {
        toast.error('Invalid Website', { description: websiteValidation.error })
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsPending(true)

    try {
      const response = await fetch('/api/businesses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit business')
      }

      toast.success('Business Submitted!', {
        description:
          'Your business has been submitted for review. You will be notified once it is approved.',
      })

      router.push('/dashboard/businesses')
    } catch (error) {
      console.error('Business submission error:', error)
      toast.error('Submission Failed', {
        description:
          error instanceof Error ? error.message : 'Failed to submit business. Please try again.',
      })
    } finally {
      setIsPending(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Basic Information</h2>

        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-1">
            Business Name *
          </label>
          <input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => updateFormData('name', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter your business name"
            required
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium mb-1">
            Description * <span className="text-gray-500">(max 500 characters)</span>
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Describe your business..."
            rows={4}
            maxLength={500}
            required
          />
          <div className="text-right text-sm text-gray-500 mt-1">
            {formData.description.length}/500
          </div>
        </div>

        <div>
          <label htmlFor="category" className="block text-sm font-medium mb-1">
            Category *
          </label>
          <select
            id="category"
            value={formData.category}
            onChange={(e) => updateFormData('category', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select a category</option>
            {BUSINESS_CATEGORIES.map((cat) => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Address */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Address</h2>

        <div>
          <label htmlFor="street" className="block text-sm font-medium mb-1">
            Street Address *
          </label>
          <input
            id="street"
            type="text"
            value={formData.address.street}
            onChange={(e) => updateFormData('address.street', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="123 Main Street"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="city" className="block text-sm font-medium mb-1">
              City *
            </label>
            <input
              id="city"
              type="text"
              value={formData.address.city}
              onChange={(e) => updateFormData('address.city', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label htmlFor="province" className="block text-sm font-medium mb-1">
              Province *
            </label>
            <input
              id="province"
              type="text"
              value={formData.address.province}
              onChange={(e) => updateFormData('address.province', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label htmlFor="postalCode" className="block text-sm font-medium mb-1">
              Postal Code *
            </label>
            <input
              id="postalCode"
              type="text"
              value={formData.address.postalCode}
              onChange={(e) => updateFormData('address.postalCode', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="M5V 3A8"
              required
            />
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Contact Information</h2>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium mb-1">
            Phone Number
          </label>
          <input
            id="phone"
            type="tel"
            value={formData.contact.phone}
            onChange={(e) => updateFormData('contact.phone', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="(*************"
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-1">
            Business Email
          </label>
          <input
            id="email"
            type="email"
            value={formData.contact.email}
            onChange={(e) => updateFormData('contact.email', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label htmlFor="website" className="block text-sm font-medium mb-1">
            Website
          </label>
          <input
            id="website"
            type="url"
            value={formData.contact.website}
            onChange={(e) => updateFormData('contact.website', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://www.yourbusiness.com"
          />
        </div>
      </div>

      {/* Business Hours */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Business Hours</h2>
        <p className="text-sm text-gray-600">
          Enter your business hours for each day. Leave blank or enter "Closed" for days you're
          closed.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {DAYS_OF_WEEK.map((day) => (
            <div key={day.key}>
              <label htmlFor={day.key} className="block text-sm font-medium mb-1">
                {day.label}
              </label>
              <input
                id={day.key}
                type="text"
                value={formData.hours[day.key as keyof typeof formData.hours]}
                onChange={(e) => updateFormData(`hours.${day.key}`, e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="9:00 AM - 5:00 PM or Closed"
              />
            </div>
          ))}
        </div>
      </div>

      <SubmitButton loading={isPending} text="Submit Business" />
    </form>
  )
}
