'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Search, MapPin, Phone, Mail, Globe, Clock } from 'lucide-react'
import Image from 'next/image'

import type { Business, Media } from '@/payload-types'

type BusinessDirectoryClientProps = {
  initialBusinesses: Business[]
  categories: { value: string; label: string }[]
  pagination: {
    page: number
    totalPages: number
    totalDocs: number
  }
  initialFilters: {
    category: string
    search: string
  }
}

export const BusinessDirectoryClient = ({
  initialBusinesses,
  categories,
  pagination,
  initialFilters,
}: BusinessDirectoryClientProps) => {
  const router = useRouter()
  const [businesses, setBusinesses] = useState(initialBusinesses)
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState(initialFilters)
  const [currentPage, setCurrentPage] = useState(pagination.page)
  const [totalPages, setTotalPages] = useState(pagination.totalPages)

  const updateURL = (newFilters: typeof filters, page = 1) => {
    const params = new URLSearchParams()
    if (newFilters.category) params.set('category', newFilters.category)
    if (newFilters.search) params.set('search', newFilters.search)
    if (page > 1) params.set('page', page.toString())

    const url = params.toString() ? `/directory?${params.toString()}` : '/directory'
    router.push(url)
  }

  const fetchBusinesses = async (newFilters: typeof filters, page = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (newFilters.category) params.set('category', newFilters.category)
      if (newFilters.search) params.set('search', newFilters.search)
      params.set('page', page.toString())
      params.set('limit', '12')

      const response = await fetch(`/api/businesses?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setBusinesses(data.businesses)
        setCurrentPage(data.page)
        setTotalPages(data.totalPages)
      }
    } catch (error) {
      console.error('Failed to fetch businesses:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateURL(filters, 1)
    fetchBusinesses(filters, 1)
  }

  const handleCategoryChange = (category: string) => {
    const newFilters = { ...filters, category }
    setFilters(newFilters)
    updateURL(newFilters, 1)
    fetchBusinesses(newFilters, 1)
  }

  const handlePageChange = (page: number) => {
    updateURL(filters, page)
    fetchBusinesses(filters, page)
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search businesses..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <Button type="submit" disabled={loading}>
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              type="button"
              variant={filters.category === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleCategoryChange('')}
            >
              All Categories
            </Button>
            {categories.map((category) => (
              <Button
                key={category.value}
                type="button"
                variant={filters.category === category.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange(category.value)}
              >
                {category.label}
              </Button>
            ))}
          </div>
        </form>
      </div>

      {/* Results */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold">
            {filters.category || filters.search
              ? `Search Results (${businesses.length} found)`
              : `All Businesses (${businesses.length})`}
          </h2>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading businesses...</p>
          </div>
        ) : businesses.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600">No businesses found matching your criteria.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {businesses.map((business) => (
              <BusinessCard key={business.id} business={business} />
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            Previous
          </Button>
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? 'default' : 'outline'}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </Button>
          ))}
          <Button
            variant="outline"
            disabled={currentPage === totalPages}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}

const BusinessCard = ({ business }: { business: Business }) => {
  const logo = business.logo as Media | null
  const firstImage = business.images?.[0]?.image as Media | null

  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
      {/* Image */}
      <div className="aspect-video bg-gray-100 relative">
        {logo?.url || firstImage?.url ? (
          <Image
            src={logo?.url || firstImage?.url || ''}
            alt={business.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-2">🏢</div>
              <div className="text-sm">No Image</div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2">{business.name}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{business.description}</p>

        <div className="space-y-2 text-sm text-gray-500">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            <span>
              {business.address.street}, {business.address.city}
            </span>
          </div>

          {business.contact?.phone && (
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>{business.contact.phone}</span>
            </div>
          )}

          {business.contact?.email && (
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span>{business.contact.email}</span>
            </div>
          )}

          {business.contact?.website && (
            <div className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              <a
                href={business.contact.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Visit Website
              </a>
            </div>
          )}
        </div>

        {/* Business Hours Preview */}
        {(business.hours?.monday || business.hours?.tuesday) && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Clock className="w-4 h-4" />
              <span>{business.hours.monday || business.hours.tuesday || 'Hours available'}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
