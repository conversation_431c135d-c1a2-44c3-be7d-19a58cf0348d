/**
 * Validation utilities for form inputs
 */

// Password validation
const PASSWORD_MIN_LENGTH = 8

export const validatePassword = (password: string): { valid: boolean; error?: string } => {
  if (!password) return { valid: false, error: 'Password is required' }
  if (password.length < PASSWORD_MIN_LENGTH) {
    return {
      valid: false,
      error: `Password must be at least ${PASSWORD_MIN_LENGTH} characters long`,
    }
  }
  if (!/[A-Z]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one uppercase letter' }
  }
  if (!/[a-z]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one lowercase letter' }
  }
  if (!/[0-9]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one number' }
  }
  if (!/[^A-Za-z0-9]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one special character' }
  }
  return { valid: true }
}

// Name validation
export const validateName = (name: string): { valid: boolean; error?: string } => {
  if (!name) return { valid: false, error: 'Name is required' }
  if (name.trim().length < 2) {
    return { valid: false, error: 'Name must be at least 2 characters long' }
  }
  if (name.trim().length > 100) {
    return { valid: false, error: 'Name must be less than 100 characters long' }
  }
  return { valid: true }
}

// Email validation
export const validateEmail = (email: string): { valid: boolean; error?: string } => {
  if (!email) return { valid: false, error: 'Email is required' }
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(email)) {
    return { valid: false, error: 'Please enter a valid email address' }
  }
  return { valid: true }
}

// Business validation functions
export const validateBusinessName = (name: string): { valid: boolean; error?: string } => {
  if (!name) return { valid: false, error: 'Business name is required' }
  if (name.trim().length < 2) {
    return { valid: false, error: 'Business name must be at least 2 characters long' }
  }
  if (name.trim().length > 100) {
    return { valid: false, error: 'Business name must be less than 100 characters long' }
  }
  return { valid: true }
}

export const validateBusinessDescription = (
  description: string,
): { valid: boolean; error?: string } => {
  if (!description) return { valid: false, error: 'Business description is required' }
  if (description.trim().length < 10) {
    return { valid: false, error: 'Business description must be at least 10 characters long' }
  }
  if (description.trim().length > 500) {
    return { valid: false, error: 'Business description must be less than 500 characters long' }
  }
  return { valid: true }
}

export const validatePostalCode = (postalCode: string): { valid: boolean; error?: string } => {
  if (!postalCode) return { valid: false, error: 'Postal code is required' }
  const canadianPostalRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/
  if (!canadianPostalRegex.test(postalCode)) {
    return { valid: false, error: 'Please enter a valid Canadian postal code (e.g., M5V 3A8)' }
  }
  return { valid: true }
}

export const validatePhone = (phone: string): { valid: boolean; error?: string } => {
  if (!phone) return { valid: true } // Optional field
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return { valid: false, error: 'Please enter a valid phone number' }
  }
  return { valid: true }
}

export const validateWebsite = (website: string): { valid: boolean; error?: string } => {
  if (!website) return { valid: true } // Optional field
  try {
    new URL(website)
    return { valid: true }
  } catch {
    return { valid: false, error: 'Please enter a valid website URL (e.g., https://example.com)' }
  }
}
