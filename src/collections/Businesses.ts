import type { CollectionConfig } from 'payload'
import type { User } from '@/payload-types'
import type { PayloadRequest } from 'payload'

const isAdmin = ({ req }: { req: PayloadRequest }): boolean => {
  const user = req.user as User | null
  return user?.role === 'admin'
}

const isOwnerOrAdmin = ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | null
  
  if (!user) return false
  if (user.role === 'admin') return true
  
  return {
    owner: {
      equals: user.id,
    },
  }
}

export const Businesses: CollectionConfig = {
  slug: 'businesses',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'status', 'owner', 'createdAt'],
  },
  access: {
    create: ({ req }) => {
      const user = req.user as User | null
      return !!user // Any authenticated user can create
    },
    read: ({ req }) => {
      const user = req.user as User | null
      
      // Admins can see all
      if (user?.role === 'admin') return true
      
      // Public can see approved businesses
      if (!user) {
        return {
          status: {
            equals: 'approved',
          },
        }
      }
      
      // Users can see their own businesses and approved ones
      return {
        or: [
          {
            owner: {
              equals: user.id,
            },
          },
          {
            status: {
              equals: 'approved',
            },
          },
        ],
      }
    },
    update: isOwnerOrAdmin,
    delete: isOwnerOrAdmin,
    admin: isAdmin,
  },
  hooks: {
    beforeChange: [
      ({ req, data }) => {
        const user = req.user as User | null
        
        // Set owner on creation
        if (req.operation === 'create' && user) {
          data.owner = user.id
        }
        
        return data
      },
    ],
    beforeValidate: [
      async ({ req, data, operation }) => {
        const user = req.user as User | null
        
        // Check business limit on creation
        if (operation === 'create' && user && user.role !== 'admin') {
          const payload = req.payload
          
          const existingBusinesses = await payload.find({
            collection: 'businesses',
            where: {
              owner: {
                equals: user.id,
              },
            },
            limit: 0, // Get count only
          })
          
          if (existingBusinesses.totalDocs >= 10) {
            throw new Error('You have reached the maximum limit of 10 businesses per account.')
          }
        }
        
        return data
      },
    ],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
      admin: {
        description: 'The name of your business',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      maxLength: 500,
      admin: {
        description: 'A brief description of your business (max 500 characters)',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Restaurant & Food', value: 'restaurant' },
        { label: 'Retail & Shopping', value: 'retail' },
        { label: 'Health & Wellness', value: 'health' },
        { label: 'Professional Services', value: 'professional' },
        { label: 'Home & Garden', value: 'home' },
        { label: 'Automotive', value: 'automotive' },
        { label: 'Beauty & Personal Care', value: 'beauty' },
        { label: 'Entertainment & Recreation', value: 'entertainment' },
        { label: 'Education & Training', value: 'education' },
        { label: 'Technology', value: 'technology' },
        { label: 'Real Estate', value: 'realestate' },
        { label: 'Financial Services', value: 'financial' },
        { label: 'Other', value: 'other' },
      ],
      admin: {
        description: 'Select the category that best describes your business',
      },
    },
    {
      name: 'address',
      type: 'group',
      fields: [
        {
          name: 'street',
          type: 'text',
          required: true,
          maxLength: 200,
        },
        {
          name: 'city',
          type: 'text',
          required: true,
          maxLength: 100,
          defaultValue: 'Toronto',
        },
        {
          name: 'province',
          type: 'text',
          required: true,
          maxLength: 50,
          defaultValue: 'Ontario',
        },
        {
          name: 'postalCode',
          type: 'text',
          required: true,
          maxLength: 10,
          validate: (val: string) => {
            const canadianPostalRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/
            if (!canadianPostalRegex.test(val)) {
              return 'Please enter a valid Canadian postal code (e.g., M5V 3A8)'
            }
            return true
          },
        },
      ],
    },
    {
      name: 'contact',
      type: 'group',
      fields: [
        {
          name: 'phone',
          type: 'text',
          maxLength: 20,
          validate: (val: string) => {
            if (!val) return true // Optional field
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
            if (!phoneRegex.test(val.replace(/[\s\-\(\)]/g, ''))) {
              return 'Please enter a valid phone number'
            }
            return true
          },
        },
        {
          name: 'email',
          type: 'email',
          admin: {
            description: 'Business contact email (optional)',
          },
        },
        {
          name: 'website',
          type: 'text',
          validate: (val: string) => {
            if (!val) return true // Optional field
            try {
              new URL(val)
              return true
            } catch {
              return 'Please enter a valid website URL (e.g., https://example.com)'
            }
          },
        },
      ],
    },
    {
      name: 'hours',
      type: 'group',
      label: 'Business Hours',
      fields: [
        {
          name: 'monday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'tuesday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'wednesday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'thursday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'friday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'saturday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
        {
          name: 'sunday',
          type: 'text',
          maxLength: 50,
          admin: { placeholder: 'e.g., 9:00 AM - 5:00 PM or Closed' },
        },
      ],
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Upload your business logo (optional)',
      },
    },
    {
      name: 'images',
      type: 'array',
      maxRows: 5,
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
      admin: {
        description: 'Upload up to 5 images of your business (optional)',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending Review', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
      ],
      access: {
        create: () => false, // Cannot be set on creation
        update: ({ req }) => {
          const user = req.user as User | null
          return user?.role === 'admin' // Only admins can change status
        },
      },
      admin: {
        description: 'Business approval status (admin only)',
      },
    },
    {
      name: 'owner',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      access: {
        create: () => false, // Set automatically
        update: ({ req }) => {
          const user = req.user as User | null
          return user?.role === 'admin' // Only admins can change owner
        },
      },
      admin: {
        readOnly: true,
        description: 'The user who submitted this business',
      },
    },
    {
      name: 'rejectionReason',
      type: 'textarea',
      maxLength: 500,
      access: {
        create: () => false,
        update: ({ req }) => {
          const user = req.user as User | null
          return user?.role === 'admin'
        },
      },
      admin: {
        condition: (data) => data.status === 'rejected',
        description: 'Reason for rejection (admin only, shown when status is rejected)',
      },
    },
  ],
}
