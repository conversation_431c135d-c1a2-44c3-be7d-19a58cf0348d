import { Section, Container } from '@/components/ds'
import { getPayload } from 'payload'
import config from '@payload-config'
import { BusinessDirectoryClient } from '@/components/business/business-directory-client'

import type { Business } from '@/payload-types'

export const dynamic = 'force-dynamic'

type SearchParams = {
  category?: string
  search?: string
  page?: string
}

export default async function BusinessDirectoryPage({
  searchParams,
}: {
  searchParams: SearchParams
}) {
  const payload = await getPayload({ config })

  // Build query for approved businesses only
  let whereClause: any = { status: { equals: 'approved' } }

  // Add category filter if specified
  if (searchParams.category) {
    whereClause = {
      and: [whereClause, { category: { equals: searchParams.category } }],
    }
  }

  // Add search filter if specified
  if (searchParams.search) {
    whereClause = {
      and: [
        whereClause,
        {
          or: [
            { name: { contains: searchParams.search } },
            { description: { contains: searchParams.search } },
          ],
        },
      ],
    }
  }

  const page = parseInt(searchParams.page || '1')
  const limit = 12

  const businesses = await payload.find({
    collection: 'businesses',
    where: whereClause,
    limit,
    page,
    sort: '-createdAt',
    populate: {
      logo: true,
      images: {
        populate: {
          image: true,
        },
      },
    },
  })

  // Get all categories for filter
  const allBusinesses = await payload.find({
    collection: 'businesses',
    where: { status: { equals: 'approved' } },
    limit: 0,
    select: {
      category: true,
    },
  })

  // Extract unique categories
  const categories = Array.from(
    new Set(allBusinesses.docs.map((business) => business.category))
  ).sort()

  const CATEGORY_LABELS: Record<string, string> = {
    restaurant: 'Restaurant & Food',
    retail: 'Retail & Shopping',
    health: 'Health & Wellness',
    professional: 'Professional Services',
    home: 'Home & Garden',
    automotive: 'Automotive',
    beauty: 'Beauty & Personal Care',
    entertainment: 'Entertainment & Recreation',
    education: 'Education & Training',
    technology: 'Technology',
    realestate: 'Real Estate',
    financial: 'Financial Services',
    other: 'Other',
  }

  return (
    <Section>
      <Container>
        <div className="space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Toronto Business Directory</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Discover local businesses in Toronto. Find restaurants, services, shops, and more in
              your neighborhood.
            </p>
          </div>

          {/* Stats */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-600">{allBusinesses.totalDocs}</div>
                <div className="text-gray-600">Total Businesses</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600">{categories.length}</div>
                <div className="text-gray-600">Categories</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600">Toronto</div>
                <div className="text-gray-600">City Focus</div>
              </div>
            </div>
          </div>

          {/* Client-side directory component */}
          <BusinessDirectoryClient
            initialBusinesses={businesses.docs}
            categories={categories.map((cat) => ({
              value: cat,
              label: CATEGORY_LABELS[cat] || cat,
            }))}
            pagination={{
              page: businesses.page || 1,
              totalPages: businesses.totalPages || 1,
              totalDocs: businesses.totalDocs || 0,
            }}
            initialFilters={{
              category: searchParams.category || '',
              search: searchParams.search || '',
            }}
          />
        </div>
      </Container>
    </Section>
  )
}
