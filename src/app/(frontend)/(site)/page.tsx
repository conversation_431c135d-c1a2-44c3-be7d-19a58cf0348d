import { Container, Section } from '@/components/ds'
import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { Search, Plus, MapPin } from 'lucide-react'

export default async function Home() {
  return (
    <Section>
      <Container>
        <div className="text-center space-y-8">
          {/* Hero Section */}
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl font-bold mb-6">Toronto Business Directory</h1>
            <p className="text-xl text-muted-foreground mb-8">
              Discover and connect with local businesses in Toronto. Find restaurants, services,
              shops, and more in your neighborhood.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/directory">
                  <Search className="w-5 h-5 mr-2" />
                  Browse Directory
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/register">
                  <Plus className="w-5 h-5 mr-2" />
                  List Your Business
                </Link>
              </Button>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Discover Local</h3>
              <p className="text-muted-foreground">
                Find businesses in your area with our easy-to-use search and filtering system.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">List Your Business</h3>
              <p className="text-muted-foreground">
                Get your business discovered by thousands of potential customers in Toronto.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Toronto Focused</h3>
              <p className="text-muted-foreground">
                Specifically designed for Toronto businesses and residents.
              </p>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  )
}
