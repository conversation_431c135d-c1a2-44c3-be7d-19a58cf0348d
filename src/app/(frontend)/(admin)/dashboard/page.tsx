import { Section, Container } from '@/components/ds'
import { Button } from '@/components/ui/button'
import { redirect } from 'next/navigation'
import { getUser } from '@/lib/auth'
import { getPayload } from 'payload'
import config from '@payload-config'
import Link from 'next/link'
import { Plus, Building2, Eye } from 'lucide-react'

import type { User } from '@/payload-types'

export const dynamic = 'force-dynamic'

export default async function Dashboard() {
  const user: User | null = await getUser()

  if (!user) {
    redirect('/login')
  }

  const payload = await getPayload({ config })

  // Get user's business count
  const businesses = await payload.find({
    collection: 'businesses',
    where: {
      owner: {
        equals: user.id,
      },
    },
    limit: 0, // Get count only
  })

  return <DashboardContent user={user} businessCount={businesses.totalDocs} />
}

const DashboardContent = ({ user, businessCount }: { user: User; businessCount: number }) => {
  const createdAt = user.createdAt ? new Date(user.createdAt) : new Date()
  const canAddMore = businessCount < 10

  return (
    <Section>
      <Container>
        <div className="space-y-8">
          {/* Welcome Section */}
          <div>
            <h1 className="text-3xl font-bold mb-2">Welcome back, {user.name || user.email}!</h1>
            <p className="text-muted-foreground">
              Manage your business listings and explore the Toronto business directory.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3">
                <Building2 className="w-8 h-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold">{businessCount}</div>
                  <div className="text-gray-600">My Businesses</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3">
                <Plus className="w-8 h-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold">{10 - businessCount}</div>
                  <div className="text-gray-600">Slots Available</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center gap-3">
                <Eye className="w-8 h-8 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold">Public</div>
                  <div className="text-gray-600">Directory</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Quick Actions</h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild>
                <Link href="/dashboard/businesses">
                  <Building2 className="w-4 h-4 mr-2" />
                  Manage My Businesses
                </Link>
              </Button>

              {canAddMore && (
                <Button variant="outline" asChild>
                  <Link href="/dashboard/businesses/new">
                    <Plus className="w-4 h-4 mr-2" />
                    Add New Business
                  </Link>
                </Button>
              )}

              <Button variant="outline" asChild>
                <Link href="/directory">
                  <Eye className="w-4 h-4 mr-2" />
                  Browse Directory
                </Link>
              </Button>
            </div>
          </div>

          {/* Account Info */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Account Information</h3>
            <div className="space-y-2 text-sm">
              <p>
                <strong>Email:</strong> {user.email}
              </p>
              <p>
                <strong>Account Created:</strong> {createdAt.toLocaleDateString()}
              </p>
              <p>
                <strong>Role:</strong> {user.role}
              </p>
              <p>
                <strong>Business Limit:</strong> {businessCount}/10 used
              </p>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  )
}
