import { Section, Container } from '@/components/ds'
import { redirect } from 'next/navigation'
import { getUser } from '@/lib/auth'
import { getPayload } from 'payload'
import config from '@payload-config'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Plus, Eye, Edit, Trash2 } from 'lucide-react'

import type { User, Business } from '@/payload-types'

export const dynamic = 'force-dynamic'

export default async function BusinessesPage() {
  const user: User | null = await getUser()

  if (!user) {
    redirect('/login')
  }

  const payload = await getPayload({ config })

  // Get user's businesses
  const businesses = await payload.find({
    collection: 'businesses',
    where: {
      owner: {
        equals: user.id,
      },
    },
    sort: '-createdAt',
    limit: 10,
  })

  const businessCount = businesses.totalDocs
  const canAddMore = businessCount < 10

  return (
    <Section>
      <Container>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">My Businesses</h1>
              <p className="text-muted-foreground">
                Manage your business listings ({businessCount}/10 used)
              </p>
            </div>
            {canAddMore && (
              <Button asChild>
                <Link href="/dashboard/businesses/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Business
                </Link>
              </Button>
            )}
          </div>

          {/* Business Limit Warning */}
          {!canAddMore && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800">
                You have reached the maximum limit of 10 businesses per account.
              </p>
            </div>
          )}

          {/* Businesses List */}
          {businesses.docs.length === 0 ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <h3 className="text-lg font-medium text-gray-900 mb-2">No businesses yet</h3>
                <p className="text-gray-500 mb-6">
                  Get started by adding your first business to the directory.
                </p>
                {canAddMore && (
                  <Button asChild>
                    <Link href="/dashboard/businesses/new">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Your First Business
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="grid gap-6">
              {businesses.docs.map((business) => (
                <BusinessCard key={business.id} business={business} />
              ))}
            </div>
          )}
        </div>
      </Container>
    </Section>
  )
}

const BusinessCard = ({ business }: { business: Business }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Approved'
      case 'pending':
        return 'Pending Review'
      case 'rejected':
        return 'Rejected'
      default:
        return status
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h3 className="text-xl font-semibold">{business.name}</h3>
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(business.status)}`}
            >
              {getStatusText(business.status)}
            </span>
          </div>
          <p className="text-gray-600 mb-2">{business.description}</p>
          <div className="text-sm text-gray-500">
            <p>
              {business.address.street}, {business.address.city}, {business.address.province}{' '}
              {business.address.postalCode}
            </p>
            {business.contact?.phone && <p>Phone: {business.contact.phone}</p>}
            {business.contact?.email && <p>Email: {business.contact.email}</p>}
            {business.contact?.website && (
              <p>
                Website:{' '}
                <a
                  href={business.contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {business.contact.website}
                </a>
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Rejection Reason */}
      {business.status === 'rejected' && business.rejectionReason && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <p className="text-red-800 text-sm">
            <strong>Rejection Reason:</strong> {business.rejectionReason}
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/businesses/${business.id}`}>
            <Eye className="w-4 h-4 mr-1" />
            View
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/businesses/${business.id}/edit`}>
            <Edit className="w-4 h-4 mr-1" />
            Edit
          </Link>
        </Button>
        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
          <Trash2 className="w-4 h-4 mr-1" />
          Delete
        </Button>
      </div>
    </div>
  )
}
