import { Section, Container } from '@/components/ds'
import { redirect } from 'next/navigation'
import { getUser } from '@/lib/auth'
import { getPayload } from 'payload'
import config from '@payload-config'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { BusinessForm } from '@/components/business/business-form'

import type { User } from '@/payload-types'

export const dynamic = 'force-dynamic'

export default async function NewBusinessPage() {
  const user: User | null = await getUser()

  if (!user) {
    redirect('/login')
  }

  const payload = await getPayload({ config })

  // Check if user has reached the business limit
  const businesses = await payload.find({
    collection: 'businesses',
    where: {
      owner: {
        equals: user.id,
      },
    },
    limit: 0, // Get count only
  })

  if (businesses.totalDocs >= 10) {
    redirect('/dashboard/businesses?error=limit-reached')
  }

  return (
    <Section>
      <Container>
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/businesses">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Businesses
          </Link>
        </Button>
        <div className="flex items-center gap-4">
          <div className="my-5">
            <h1 className="text-3xl font-bold">Add New Business</h1>
            <p className="text-muted-foreground">
              Submit your business information for review and approval.
            </p>
          </div>
        </div>

        {/* Business Limit Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-5">
          <p className="text-blue-800">
            You can submit up to 10 businesses per account. You currently have{' '}
            <strong>{businesses.totalDocs}</strong> business{businesses.totalDocs !== 1 ? 'es' : ''}{' '}
            submitted.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <BusinessForm />
        </div>
      </Container>
    </Section>
  )
}
