import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { cookies } from 'next/headers'
import type { User } from '@/payload-types'
import {
  validateBusinessName,
  validateBusinessDescription,
  validatePostalCode,
  validatePhone,
  validateWebsite,
  validateEmail,
} from '@/lib/validation'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Get user from cookie
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Verify token and get user
    let user: User | null = null
    try {
      const result = await payload.verifyToken(token)
      if (result.user) {
        user = result.user as User
      }
    } catch (error) {
      return NextResponse.json({ error: 'Invalid authentication token' }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { name, description, category, address, contact, hours } = body

    // Validate required fields
    const nameValidation = validateBusinessName(name)
    if (!nameValidation.valid) {
      return NextResponse.json({ error: nameValidation.error }, { status: 400 })
    }

    const descriptionValidation = validateBusinessDescription(description)
    if (!descriptionValidation.valid) {
      return NextResponse.json({ error: descriptionValidation.error }, { status: 400 })
    }

    if (!category) {
      return NextResponse.json({ error: 'Category is required' }, { status: 400 })
    }

    if (!address?.street?.trim()) {
      return NextResponse.json({ error: 'Street address is required' }, { status: 400 })
    }

    if (!address?.city?.trim()) {
      return NextResponse.json({ error: 'City is required' }, { status: 400 })
    }

    if (!address?.province?.trim()) {
      return NextResponse.json({ error: 'Province is required' }, { status: 400 })
    }

    const postalCodeValidation = validatePostalCode(address.postalCode)
    if (!postalCodeValidation.valid) {
      return NextResponse.json({ error: postalCodeValidation.error }, { status: 400 })
    }

    // Validate optional contact fields if provided
    if (contact?.phone) {
      const phoneValidation = validatePhone(contact.phone)
      if (!phoneValidation.valid) {
        return NextResponse.json({ error: phoneValidation.error }, { status: 400 })
      }
    }

    if (contact?.email) {
      const emailValidation = validateEmail(contact.email)
      if (!emailValidation.valid) {
        return NextResponse.json({ error: emailValidation.error }, { status: 400 })
      }
    }

    if (contact?.website) {
      const websiteValidation = validateWebsite(contact.website)
      if (!websiteValidation.valid) {
        return NextResponse.json({ error: websiteValidation.error }, { status: 400 })
      }
    }

    // Check business limit (non-admin users only)
    if (user.role !== 'admin') {
      const existingBusinesses = await payload.find({
        collection: 'businesses',
        where: {
          owner: {
            equals: user.id,
          },
        },
        limit: 0, // Get count only
      })

      if (existingBusinesses.totalDocs >= 10) {
        return NextResponse.json(
          { error: 'You have reached the maximum limit of 10 businesses per account.' },
          { status: 400 }
        )
      }
    }

    // Create the business
    const business = await payload.create({
      collection: 'businesses',
      data: {
        name: name.trim(),
        description: description.trim(),
        category,
        address: {
          street: address.street.trim(),
          city: address.city.trim(),
          province: address.province.trim(),
          postalCode: address.postalCode.trim().toUpperCase(),
        },
        contact: {
          phone: contact?.phone?.trim() || null,
          email: contact?.email?.trim() || null,
          website: contact?.website?.trim() || null,
        },
        hours: {
          monday: hours?.monday?.trim() || null,
          tuesday: hours?.tuesday?.trim() || null,
          wednesday: hours?.wednesday?.trim() || null,
          thursday: hours?.thursday?.trim() || null,
          friday: hours?.friday?.trim() || null,
          saturday: hours?.saturday?.trim() || null,
          sunday: hours?.sunday?.trim() || null,
        },
        status: 'pending',
        owner: user.id,
      },
    })

    return NextResponse.json({
      success: true,
      business: {
        id: business.id,
        name: business.name,
        status: business.status,
      },
    })
  } catch (error) {
    console.error('Business creation error:', error)
    
    if (error instanceof Error && error.message.includes('maximum limit')) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json(
      { error: 'Failed to create business. Please try again.' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    // Get user from cookie (optional for public access)
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    
    let user: User | null = null
    if (token) {
      try {
        const result = await payload.verifyToken(token)
        if (result.user) {
          user = result.user as User
        }
      } catch (error) {
        // Token invalid, continue as public user
      }
    }

    // Build query based on user permissions
    let whereClause: any = {}
    
    if (user?.role === 'admin') {
      // Admins can see all businesses
    } else if (user) {
      // Authenticated users can see their own businesses and approved ones
      whereClause = {
        or: [
          { owner: { equals: user.id } },
          { status: { equals: 'approved' } },
        ],
      }
    } else {
      // Public users can only see approved businesses
      whereClause = { status: { equals: 'approved' } }
    }

    // Add category filter if specified
    const category = searchParams.get('category')
    if (category) {
      whereClause = {
        and: [whereClause, { category: { equals: category } }],
      }
    }

    // Add search filter if specified
    const search = searchParams.get('search')
    if (search) {
      whereClause = {
        and: [
          whereClause,
          {
            or: [
              { name: { contains: search } },
              { description: { contains: search } },
            ],
          },
        ],
      }
    }

    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')

    const businesses = await payload.find({
      collection: 'businesses',
      where: whereClause,
      limit,
      page,
      sort: '-createdAt',
      populate: {
        owner: {
          select: {
            name: true,
            email: true,
          },
        },
        logo: true,
      },
    })

    return NextResponse.json({
      success: true,
      businesses: businesses.docs,
      totalPages: businesses.totalPages,
      page: businesses.page,
      totalDocs: businesses.totalDocs,
    })
  } catch (error) {
    console.error('Business fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch businesses. Please try again.' },
      { status: 500 }
    )
  }
}
