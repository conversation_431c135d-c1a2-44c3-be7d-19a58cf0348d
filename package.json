{"name": "payload-starter", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "sharp"]}, "dependencies": {"@payloadcms/db-postgres": "3.50.0", "@payloadcms/next": "3.50.0", "@payloadcms/payload-cloud": "3.50.0", "@payloadcms/richtext-lexical": "3.50.0", "@payloadcms/storage-s3": "3.50.0", "@payloadcms/storage-vercel-blob": "3.50.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "graphql": "^16.11.0", "lucide-react": "^0.474.0", "next": "15.4.5", "next-themes": "^0.4.6", "payload": "3.50.0", "postcss": "^8.5.6", "react": "19.1.1", "react-dom": "19.1.1", "react-wrap-balancer": "^1.1.1", "resend": "^4.8.0", "sharp": "0.33.5", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^22.17.0", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "prettier": "^3.6.2", "tw-animate-css": "^1.3.6", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0"}}